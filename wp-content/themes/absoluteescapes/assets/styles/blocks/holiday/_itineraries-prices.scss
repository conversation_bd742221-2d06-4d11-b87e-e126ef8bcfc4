.itineraries-prices {
    position: relative;
    //background: #efefef;

    &:after {
        content: "";
        display: block;
        position: absolute;
        top: 0;
        left: -500%;
        z-index: 0;
        width: 1000%;
        height: 100%;
        // background: inherit;
    }

    &__inner {
        position: relative;
        z-index: 2;
        padding: 65px 0;

        @media only screen and (max-width: map-get($grid-breakpoints-max, lg)) {
            padding: 45px 0;
        }
    }

    &__container {
        @media only screen and (max-width: map-get($grid-breakpoints-max, sm)) {
            padding: 0 20px;
        }
    }

    .itineraries-prices__container--table {
        @media only screen and (max-width: map-get($grid-breakpoints-max, sm)) {
            width: 100%;
            max-width: 100%;
            padding: 0;
        }
    }

    .accordion {
        margin: 0;

        &__item {
            flex: 0 0 100%;
            max-width: 100%;
            padding: 0;
            border-top: 1px solid $midlightgrey;
            transition: 300ms;

            &:after {
                display: none;
            }

            &.active {
                border-color: $teal;
                background: $teal;


                .accordion__heading-wrapper {
                    .itineraries-prices__table-row {
                        *
                        span {
                            color: $white;
                        }

                        span.itineraries-prices__difficulty-icon {
                            color: $bluegrey;
                        }
                    }

                    .fa-chevron-down {
                        transform: rotate(180deg);
                        color: $white;
                    }
                }
            }
        }

        &__heading-wrapper {
            position: relative;

            .fa-chevron-down {
                position: absolute;
                top: 0;
                right: 20px;
                bottom: 0;
                margin: auto 0;
                color: $bluegrey;
                transition: 300ms;

                @media only screen and (max-width: map-get($grid-breakpoints-max, xl)) {
                    right: 15px;
                }

                @media only screen and (max-width: map-get($grid-breakpoints-max, lg)) {
                    font-size: 1.2rem;
                }

                @media only screen and (max-width: 360px) {
                    right: 8px;
                }
            }
        }
    }

    &__table-row,
    &__prices-row {
        display: flex;
        flex-wrap: wrap;
        padding: 15px 0;
        font-size: 1.8rem;

        color: $bluegrey;


        @media only screen and (max-width: map-get($grid-breakpoints-max, md)) {
            font-size: 1.3rem;
        }

        &--heading {
            span {
                font-weight: 600;
                color: $teal;
            }
        }
    }

    &__table-row {
        padding-right: 30px;

        @media only screen and (max-width: map-get($grid-breakpoints-max, md)) {
            font-size: 1.6rem;
        }

        @media only screen and (max-width: map-get($grid-breakpoints-max, sm)) {
            padding: 10px 10px;
        }
    }

    &__table-col,
    &__prices-col {
        flex: 1 0;
        padding: 0 10px;

        @media only screen and (max-width: map-get($grid-breakpoints-max, md)) {
            padding: 0 8px;
            font-size: 1.3rem;
        }

        @media only screen and (max-width: map-get($grid-breakpoints-max, sm)) {
            padding: 0 5px;
            font-size: 1.3rem;
        }
    }

    &__table-col {

        @media only screen and (max-width: map-get($grid-breakpoints-max, md)) {

            font-size: 1.6rem;
        }

        @media only screen and (max-width: map-get($grid-breakpoints-max, sm)) {
            font-size: 1.6rem;
        }

        &--desktop {
            @media only screen and (max-width: map-get($grid-breakpoints-max, md)) {
                display: none;
            }
        }

        &--difficulty-mobile {
            @media only screen and (max-width: map-get($grid-breakpoints-max, md)) {
                display: block;
                flex: 0 0 auto;
                min-width: 120px; // Fixed width for consistent alignment
            }
        }

        &--mobile {
            display: none;
            padding-top: 15px;
            padding-bottom: 15px;
            border-top: 1px solid $white;

            @media only screen and (max-width: map-get($grid-breakpoints-max, sm)) {
                padding-right: 15px;
                padding-left: 15px;
            }

            span {

                color: $white;

                &.itineraries-prices__difficulty-icon {
                    color: $bluegrey;
                }
            }


            @media only screen and (max-width: map-get($grid-breakpoints-max, md)) {
                display: block;
                flex: 0 0 100%;
                max-width: 100%;
            }
        }
    }

    &__table-col-item {
        display: block;
        margin-bottom: 3px;
    }

    &__expanded {
        position: relative;
        padding: 40px 35px;
        border: 1px solid $midlightgrey;
        background: $white;

        @media only screen and (max-width: map-get($grid-breakpoints-max, xl)) {
            padding: 45px 25px 35px;
        }

        @media only screen and (max-width: map-get($grid-breakpoints-max, sm)) {
            padding: 45px 20px 35px;
        }
    }

    &__expanded-col {
        flex: 0 0 50%;
        max-width: 50%;
        padding: 0 15px;

        @media only screen and (max-width: map-get($grid-breakpoints-max, xl)) {
            flex: 0 0 100%;
            max-width: 100%;
        }


        @media only screen and (max-width: map-get($grid-breakpoints-max, lg)) {
            flex: 0 0 50%;
            max-width: 50%;
        }


        @media only screen and (max-width: map-get($grid-breakpoints-max, md)) {
            flex: 0 0 100%;
            max-width: 100%;
        }

        @media only screen and (max-width: map-get($grid-breakpoints-max, sm)) {
            padding: 0
        }
    }

    &__number-wrapper {
        margin-right: 20px;
    }

    &__number {
        display: block;
        position: relative;
        width: 26px;
        height: 26px;
        line-height: 26px;
        border-radius: 50%;
        text-align: center;
        font-weight: bold;
        background: $blue;
        color: $white;

        &:after {
            content: "";
            display: block;
            position: absolute;
            top: 0;
            right: 0;
            left: 0;
            width: 1px;
            height: 1000%;
            margin: 0 auto;
            background: $midlightgrey;
            transform: translateY(10%);
        }


    }

    &__points {
        max-width: 250px;
        margin: 0 auto;
    }

    &__point {
        display: flex;
        flex-wrap: wrap;
        padding-bottom: 15px;
        overflow: hidden;

        &:first-child {
            .itineraries-prices__number {
                border: 3px solid $blue;
                background: $white;
            }
        }

        &:last-child {
            .itineraries-prices__number {
                &:after {
                    display: none;
                }

                border: 3px solid $blue;
                background: $white;
            }
        }
    }

    &__text-wrapper {
        flex: 1 0;
    }

    .itineraries-prices__prices {
        padding-top: 15px;

        .accordion__item {
            &:last-child {
                border-bottom: 1px solid $midlightgrey;
            }

            .accordion__heading-wrapper {
                .fa-chevron-down {
                    position: static;
                    transform: rotate(0);
                    color: $teal;
                    margin: 0 20px;
                }
            }

            &.active {
                .itineraries-prices__prices-col {
                    span {
                        color: $white;
                    }

                    &--button {
                        .button {
                            background: transparent;
                            color: $white;
                            border: 2px solid $white;

                            &:hover, &:focus {
                                background: $white;
                                color: $teal;
                            }
                        }
                    }
                }

                .accordion__heading-wrapper {
                    .fa-chevron-down {
                        transform: rotate(180deg);
                        color: $white;
                    }
                }
            }
        }

        .accordion__copy {
            padding: 30px 15px;
            background: $white;

            @media only screen and (max-width: map-get($grid-breakpoints-max, sm)) {
                padding: 25px 0;
            }

            &:after {
                content: '';
                display: table;
                clear: both;
            }

            .accordion__button-wrapper {
                text-align: right;
            }

            .button {
                background: $blue;
                color: $white;

                &:hover, &:focus {
                    background: $teal;
                }
            }
        }
    }

    .itineraries-prices__prices-row {
        + .fa-chevron-down {
            color: $teal;
        }

        &--offwhite {
            background: $offwhitethree;
        }
    }

    &__prices-col {
        flex: 1;
        padding: 0 8px;

        &:first-child {
            flex: 0.6; // Narrower first column

            span {
                color: $teal;
            }
        }

        &:nth-child(2) {
            flex: 0.8; // Narrower venue column
        }

        &:nth-child(3) {
            flex: 0.8; // Narrower price column
        }

        &--button {
            flex: 0 0 120px; // Wider button column
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 15px; // 15px horizontal padding

            .button {
                font-size: 1.4rem;
                padding: 8px 16px;
                margin: 0;
                white-space: nowrap;
                min-width: 90px;
                text-align: center;
                position: relative;
                z-index: 10; // Ensure button is above accordion click handler
            }
        }

        &--arrow {
            flex: 0 0 60px; // Wider arrow column to accommodate margins
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0;

            .fa-chevron-down {
                font-size: 1.2rem;
                margin: 0 20px; // 20px left and right margin
            }
        }

        span {
            font-weight: 600;
            color: $bluegrey;
        }
    }

    &__copy {
        &--footer {
            padding-top: 45px;
            text-align: right;

            p,
            li {
                font-size: 1.8rem;
                color: $bluegrey;


                @media only screen and (max-width: map-get($grid-breakpoints-max, sm)) {
                    font-size: 1.6rem;
                }
            }
        }
    }

    &__difficulty-wrapper {
        display: inline-block;
        vertical-align: top;
    }

    &__difficulty {
        display: block;
        font-size: 1.4rem;
        text-align: center;
        line-height: 1;

        // Override for mobile difficulty column
        .itineraries-prices__table-col--difficulty-mobile & {
            display: inline-block !important;
            text-align: left !important;
            font-size: 1.1rem !important;
            vertical-align: middle !important;
        }
    }

    &__difficulty-score {
        display: block;
        text-align: center;

        // Override for mobile difficulty column
        .itineraries-prices__table-col--difficulty-mobile & {
            display: inline-block !important;
            text-align: left !important;
            // Removed margin-right for better alignment
            vertical-align: middle !important;
        }
    }

    &__difficulty-icon {
        display: inline-block;
        position: relative;
        width: 28px;
        height: 28px;
        font-size: 1.2rem;
        border-radius: 50%;
        border: 1px solid $bluegrey;
        background: $white;

        svg {
            position: absolute;
            top: 0;
            right: 0;
            bottom: 0;
            left: 0;
            margin: auto;
        }

        i {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }

        // Mobile specific sizing and centering
        @media only screen and (max-width: map-get($grid-breakpoints-max, md)) {
            display: inline-flex;
            align-items: center;
            justify-content: center;

            i {
                position: static;
                transform: none;
            }
        }
    }

    // Desktop-specific styling for arrow column and difficulty alignment
    @media only screen and (min-width: map-get($grid-breakpoints, lg)) {
        &__table-col--arrow {
            flex: 0 0 20px !important; // Fixed 20px width on desktop
            max-width: 20px;
        }

        &__table-col--difficulty-mobile {
            text-align: center !important; // Override mobile left alignment for desktop
            display: flex !important;
            flex-direction: column !important;
            align-items: center !important;
            flex: 1.15 0 !important; // Increased by 15% (from 1.0 to 1.15)
        }

        // Left align difficulty column header only
        &__table-row--heading &__table-col--difficulty-mobile {
            text-align: left !important;
            align-items: flex-start !important;
        }

        &__difficulty {
            font-size: 1.4rem !important; // Restore original desktop size
        }

        // Desktop column width adjustments
        // Reduce tour code and itinerary by 15% each, add to average miles and difficulty
        &__table-col:nth-child(1) { // Tour code column
            flex: 0.85 0 !important; // Reduced by 15% (from 1.0 to 0.85)
        }

        &__table-col:nth-child(2) { // Itinerary column
            flex: 0.85 0 !important; // Reduced by 15% (from 1.0 to 0.85)
        }

        &__table-col:nth-child(3) { // Average miles column
            flex: 1.15 0 !important; // Increased by 15% (from 1.0 to 1.15)
        }
    }

    // New itinerary styling
    &__itinerary-main {
        .fa-sun {
            color: #ffa500; // Orange for sun
            margin: 0 2px;
        }

        .fa-moon {
            color: #4169e1; // Royal blue for moon
            margin: 0 2px;
        }
    }

    &__itinerary-extra {
        margin-left: 5px;

        img {
            max-width: 12px;
            opacity: 0.7;
            transition: opacity 0.3s ease;
        }

        &:hover img {
            opacity: 1;
        }
    }

    // Mobile layout adjustments - Table-like column control
    @media only screen and (max-width: map-get($grid-breakpoints-max, md)) {
        &__table-row {
            display: flex;
            flex-wrap: nowrap; // Prevent wrapping to maintain column structure
            align-items: center; // Vertically center all row contents
            width: 100%;
            min-height: 50px; // Ensure consistent row height
        }

        // CENTRALLY CONTROLLED COLUMN WIDTHS (like a proper table)
        // Column 1: Itinerary - reduced width to give more space to difficulty
        &__table-col:nth-child(2) { // Itinerary column (2nd child after hidden tour code)
            flex: 0 0 27% !important; // Reduced from 30% to 27% (-3%)
            min-width: 0;
            padding-right: 10px;
            display: flex;
            align-items: center; // Vertically center itinerary content
        }

        // Column 2: Difficulty - increased width for better content fit
        &__table-col--difficulty-mobile {
            flex: 0 0 38% !important; // Increased from 35% to 38% (+3%)
            text-align: left !important;
            padding-left: 0 !important; // Remove padding to align with header
            padding-right: 10px;
            white-space: nowrap; // Prevent text wrapping
            display: flex;
            align-items: center; // Vertically center difficulty content

            // Header and content alignment
            span {
                text-align: left !important;
                display: inline-block; // Ensure span displays properly
            }

            .itineraries-prices__difficulty-score {
                display: flex !important; // Use flex for better alignment
                align-items: center !important; // Vertically center icons
                margin-right: 8px !important; // Restore spacing between icons and text on mobile
                vertical-align: middle !important;

                .itineraries-prices__difficulty-icon {
                    width: 26px; // Increased from 24px
                    height: 26px; // Increased from 24px
                    font-size: 1.2rem; // Increased from 1.1rem
                    display: inline-flex !important; // Use flex for centering
                    align-items: center !important; // Vertically center icon
                    justify-content: center !important; // Horizontally center icon
                    margin-right: 2px; // Small space between individual icons

                    i {
                        position: static !important; // Override absolute positioning
                        transform: none !important; // Remove transform
                        margin: 0 !important; // Remove any default margins
                        padding: 0 !important; // Remove any default padding
                        line-height: 1 !important; // Ensure consistent line height
                        vertical-align: baseline !important; // Reset vertical alignment
                    }
                }
            }

            .itineraries-prices__difficulty {
                display: inline-block !important; // Force inline display
                font-size: 1.2rem !important; // Increased from 1.1rem
                vertical-align: middle !important;
                text-align: left !important;
            }
        }

        // Column 3: Price - wider column for better alignment
        &__table-col--price {
            flex: 0 0 30% !important; // Increased from 25% to 30% (35px wider)
            text-align: right !important; // Ensure right alignment
            padding-right: 10px; // Minimal padding since arrow is separate
            padding-left: 15px;
            white-space: nowrap; // Prevent text wrapping
            display: flex;
            align-items: center; // Vertically center price content
            justify-content: flex-end; // Right align content within flex container
        }

        // Column 4: Arrow - dedicated column for dropdown arrows
        &__table-col--arrow {
            flex: 0 0 5% !important; // Small dedicated column for arrows on mobile
            display: flex;
            align-items: center; // Vertically center arrow
            justify-content: center; // Center arrow horizontally
            padding: 0; // No padding needed
        }

        // Ensure all columns in header row follow same widths
        &__table-row--heading {
            .itineraries-prices__table-col:nth-child(2) {
                flex: 0 0 27% !important; // Match content row width (reduced)
                padding-right: 10px;
            }

            .itineraries-prices__table-col--difficulty-mobile {
                flex: 0 0 38% !important; // Match content row width (increased)
                text-align: left !important;
                padding-left: 0 !important;
                padding-right: 10px;
                white-space: nowrap; // Prevent header text wrapping

                span {
                    text-align: left !important;
                }
            }

            .itineraries-prices__table-col--price {
                flex: 0 0 30% !important; // Match content row width (increased)
                text-align: right !important; // Ensure right alignment
                padding-right: 10px; // Match content row padding
                padding-left: 15px; // Match content row padding
                white-space: nowrap; // Prevent header text wrapping
                display: flex;
                align-items: center; // Vertically center header content
                justify-content: flex-end; // Right align header content
            }

            .itineraries-prices__table-col--arrow {
                flex: 0 0 5% !important; // Match content row width
                display: flex;
                align-items: center;
                justify-content: center;
            }
        }
    }
}
